@extends('layouts.layoutMaster')

@section('title', __('Employment Application'))

@section('vendor-style')
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/bs-stepper/bs-stepper.css')}}" />
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')}}" />
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/flatpickr/flatpickr.css')}}"/>
  <style>
    .bs-stepper.vertical .bs-stepper-header {
      min-width: 20rem;
    }
    @media (max-width: 992px) {
      .bs-stepper.vertical {
        flex-direction: column;
      }
      .bs-stepper.vertical .bs-stepper-header {
        min-width: 100%;
        margin-bottom: 1.5rem;
      }
    }
    .bs-stepper.vertical .line {
      min-height: 1.5rem;
    }
  </style>
@endsection

@section('vendor-script')
  <script src="{{ asset('assets/vendor/libs/bs-stepper/bs-stepper.js') }}"></script>
  <script src="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
  <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@section('page-script')
  <script src="{{ asset('assets/js/employment-application.js') }}"></script>
@endsection

@section('content')
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
      </li>
      <li class="breadcrumb-item active">{{ __('Employment Application') }}</li>
    </ol>
  </nav>

  <div class="card">
    <div class="card-header">
      <h4 class="card-title">{{ __('Employment Application Form') }}</h4>
    </div>
    <div class="card-body">
      <div class="bs-stepper vertical wizard-numbered">
        <div class="bs-stepper-header">
          <!-- Steps -->
          <div class="step" data-target="#basic-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">1</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Basic Information') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Personal & Address Details') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#education">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">2</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Certificates') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Educational Information') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#courses">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">3</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Courses') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Training Information') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#experience">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">4</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Experience') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Work History') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#languages">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">5</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Languages') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Language Proficiency') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#references">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">6</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('References') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Professional References') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#other-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">7</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Other Information') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Additional Details') }}</span>
              </span>
            </button>
          </div>
          <div class="line"></div>

          <div class="step" data-target="#review">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-circle">8</span>
              <span class="bs-stepper-label">
                <span class="bs-stepper-title">{{ __('Review') }}</span>
                <span class="bs-stepper-subtitle">{{ __('Submit Application') }}</span>
              </span>
            </button>
          </div>
        </div>

        <div class="bs-stepper-content">
          <form id="employment-application-form" action="{{ route('employment-application.store') }}" method="POST" novalidate>
            @csrf
            
            <!-- Basic Information -->
            <div id="basic-info" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('Basic Information') }}</h6>
                  <small>{{ __('Enter your personal and address details') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-basic-info-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-md-12">
                  <h5 class="mb-3">{{ __('Personal Information') }}</h5>
                </div>

                <div class="col-md-3">
                  <label class="form-label" for="first_name">{{ __('First Name') }} <span class="text-danger">*</span></label>
                  <input type="text" id="first_name" name="first_name" class="form-control" placeholder="{{ __('First Name') }}" required />
                </div>
                
                <div class="col-md-3">
                  <label class="form-label" for="father_name">{{ __('Father\'s Name') }} <span class="text-danger">*</span></label>
                  <input type="text" id="father_name" name="father_name" class="form-control" placeholder="{{ __('Father\'s Name') }}" required />
                </div>
                
                <div class="col-md-3">
                  <label class="form-label" for="grandfather_name">{{ __('Grandfather\'s Name') }} <span class="text-danger">*</span></label>
                  <input type="text" id="grandfather_name" name="grandfather_name" class="form-control" placeholder="{{ __('Grandfather\'s Name') }}" required />
                </div>
                
                <div class="col-md-3">
                  <label class="form-label" for="family_name">{{ __('Family Name') }} <span class="text-danger">*</span></label>
                  <input type="text" id="family_name" name="family_name" class="form-control" placeholder="{{ __('Family Name') }}" required />
                </div>
                
                <div class="col-md-3">
                  <label class="form-label" for="mother_name">{{ __('Mother Name') }} <span class="text-danger">*</span></label>
                  <input type="text" id="mother_name" name="mother_name" class="form-control" placeholder="{{ __('Mother Name') }}" required />
                </div>
                
                <div class="col-md-3">
                  <label class="form-label" for="nationality">{{ __('Nationality') }} <span class="text-danger">*</span></label>
                  <select id="nationality" name="nationality" class="form-select" required>
                    <option value="">{{ __('Select Nationality') }}</option>
                    <option value="Saudi">{{ __('Saudi') }}</option>
                    <!-- Add more nationalities as needed -->
                  </select>
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="birth_date">{{ __('Date of Birth') }} <span class="text-danger">*</span></label>
                  <input type="text" id="birth_date" name="birth_date" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="birth_place">{{ __('Place of Birth') }} <span class="text-danger">*</span></label>
                  <input type="text" id="birth_place" name="birth_place" class="form-control" placeholder="{{ __('Place of Birth') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="religion">{{ __('Religion') }} <span class="text-danger">*</span></label>
                  <select id="religion" name="religion" class="form-select" required>
                    <option value="">{{ __('Select Religion') }}</option>
                    <option value="Islam">{{ __('Islam') }}</option>
                    <!-- Add more religions as needed -->
                  </select>
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="gender">{{ __('Gender') }} <span class="text-danger">*</span></label>
                  <select id="gender" name="gender" class="form-select" required>
                    <option value="">{{ __('Select Gender') }}</option>
                    <option value="male">{{ __('Male') }}</option>
                    <option value="female">{{ __('Female') }}</option>
                  </select>
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="marital_status">{{ __('Marital Status') }} <span class="text-danger">*</span></label>
                  <select id="marital_status" name="marital_status" class="form-select" required>
                    <option value="">{{ __('Select Marital Status') }}</option>
                    <option value="single">{{ __('Single') }}</option>
                    <option value="married">{{ __('Married') }}</option>
                    <option value="divorced">{{ __('Divorced') }}</option>
                    <option value="widowed">{{ __('Widowed') }}</option>
                  </select>
                </div>
                
                <div class="col-md-4" id="children_div" style="display: none;">
                  <label class="form-label" for="children_count">{{ __('Number of Children') }}</label>
                  <input type="number" id="children_count" name="children_count" class="form-control" min="0" placeholder="{{ __('Number of Children') }}" />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="id_type">{{ __('ID Type') }} <span class="text-danger">*</span></label>
                  <select id="id_type" name="id_type" class="form-select" required>
                    <option value="">{{ __('Select ID Type') }}</option>
                    <option value="national_id">{{ __('National ID') }}</option>
                    <option value="iqama">{{ __('Iqama') }}</option>
                    <option value="passport">{{ __('Passport') }}</option>
                  </select>
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="id_number">{{ __('ID Number') }} <span class="text-danger">*</span></label>
                  <input type="text" id="id_number" name="id_number" class="form-control" placeholder="{{ __('ID Number') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="id_issue_date">{{ __('ID Issue Date') }} <span class="text-danger">*</span></label>
                  <input type="text" id="id_issue_date" name="id_issue_date" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="id_issue_place">{{ __('ID Issue Place') }} <span class="text-danger">*</span></label>
                  <input type="text" id="id_issue_place" name="id_issue_place" class="form-control" placeholder="{{ __('ID Issue Place') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="id_expiry_date">{{ __('ID Expiry Date') }} <span class="text-danger">*</span></label>
                  <input type="text" id="id_expiry_date" name="id_expiry_date" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" required />
                </div>
                
                <div class="col-md-12">
                  <h5 class="mb-3 mt-3">{{ __('Address Information') }}</h5>
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="country">{{ __('Country') }} <span class="text-danger">*</span></label>
                  <select id="country" name="country" class="form-select" required>
                    <option value="">{{ __('Select Country') }}</option>
                    <option value="Saudi Arabia">{{ __('Saudi Arabia') }}</option>
                    <!-- Add more countries as needed -->
                  </select>
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="city">{{ __('City') }} <span class="text-danger">*</span></label>
                  <input type="text" id="city" name="city" class="form-control" placeholder="{{ __('City') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="district">{{ __('District') }} <span class="text-danger">*</span></label>
                  <input type="text" id="district" name="district" class="form-control" placeholder="{{ __('District') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="street">{{ __('Street') }} <span class="text-danger">*</span></label>
                  <input type="text" id="street" name="street" class="form-control" placeholder="{{ __('Street') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="po_box">{{ __('P.O. Box') }}</label>
                  <input type="text" id="po_box" name="po_box" class="form-control" placeholder="{{ __('P.O. Box') }}" />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="postal_code">{{ __('Postal Code') }}</label>
                  <input type="text" id="postal_code" name="postal_code" class="form-control" placeholder="{{ __('Postal Code') }}" />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="home_phone">{{ __('Home Phone') }}</label>
                  <input type="text" id="home_phone" name="home_phone" class="form-control" placeholder="{{ __('Home Phone') }}" />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="mobile_phone">{{ __('Mobile Phone') }} <span class="text-danger">*</span></label>
                  <input type="text" id="mobile_phone" name="mobile_phone" class="form-control" placeholder="{{ __('Mobile Phone') }}" required />
                </div>
                
                <div class="col-md-4">
                  <label class="form-label" for="email">{{ __('Email') }} <span class="text-danger">*</span></label>
                  <input type="email" id="email" name="email" class="form-control" placeholder="{{ __('Email') }}" required />
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect" disabled>
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Education/Certificates Tab -->
            <div id="education" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('Certificates') }}</h6>
                  <small>{{ __('Enter your educational qualifications') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-certificates-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="mb-0">{{ __('Educational Certificates') }}</h5>
                      <button type="button" class="btn btn-primary btn-sm" id="add-certificate">
                        <i class="ti ti-plus me-1"></i>{{ __('Add Certificate') }}
                      </button>
                    </div>
                    <div class="card-body">
                      <div id="certificates-container">
                        <div class="certificate-item border rounded p-3 mb-3">
                          <div class="row g-3">
                            <div class="col-md-6">
                              <label class="form-label" for="certificate_name_0">{{ __('Certificate Name') }} <span class="text-danger">*</span></label>
                              <input type="text" id="certificate_name_0" name="certificates[0][name]" class="form-control" placeholder="{{ __('Certificate Name') }}" required />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="certificate_institution_0">{{ __('Institution Name') }} <span class="text-danger">*</span></label>
                              <input type="text" id="certificate_institution_0" name="certificates[0][institution]" class="form-control" placeholder="{{ __('Institution Name') }}" required />
                            </div>

                            <div class="col-md-6">
                              <label class="form-label" for="certificate_graduation_date_0">{{ __('Graduation Date') }} <span class="text-danger">*</span></label>
                              <input type="text" id="certificate_graduation_date_0" name="certificates[0][graduation_date]" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" required />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="certificate_grade_0">{{ __('Grade/GPA') }} <span class="text-danger">*</span></label>
                              <input type="text" id="certificate_grade_0" name="certificates[0][grade]" class="form-control" placeholder="{{ __('Grade/GPA') }}" required />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Courses Tab -->
            <div id="courses" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('Courses') }}</h6>
                  <small>{{ __('Enter your training courses information') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-courses-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="mb-0">{{ __('Training Courses') }}</h5>
                      <button type="button" class="btn btn-primary btn-sm" id="add-course">
                        <i class="ti ti-plus me-1"></i>{{ __('Add Course') }}
                      </button>
                    </div>
                    <div class="card-body">
                      <div id="courses-container">
                        <div class="course-item border rounded p-3 mb-3">
                          <div class="row g-3">
                            <div class="col-md-6">
                              <label class="form-label" for="course_name_0">{{ __('Course Name') }} <span class="text-danger">*</span></label>
                              <input type="text" id="course_name_0" name="courses[0][name]" class="form-control" placeholder="{{ __('Course Name') }}" required />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="course_institution_0">{{ __('Training Institution') }} <span class="text-danger">*</span></label>
                              <input type="text" id="course_institution_0" name="courses[0][institution]" class="form-control" placeholder="{{ __('Training Institution') }}" required />
                            </div>

                            <div class="col-md-6">
                              <label class="form-label" for="course_completion_date_0">{{ __('Completion Date') }} <span class="text-danger">*</span></label>
                              <input type="text" id="course_completion_date_0" name="courses[0][completion_date]" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" required />
                            </div>

                            <div class="col-md-6">
                              <label class="form-label" for="course_duration_0">{{ __('Duration') }}</label>
                              <input type="text" id="course_duration_0" name="courses[0][duration]" class="form-control" placeholder="{{ __('e.g., 40 hours, 2 weeks') }}" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Experience Tab -->
            <div id="experience" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('Experience') }}</h6>
                  <small>{{ __('Enter your work experience') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-experience-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="mb-0">{{ __('Work Experience') }}</h5>
                      <button type="button" class="btn btn-primary btn-sm" id="add-experience">
                        <i class="ti ti-plus me-1"></i>{{ __('Add Experience') }}
                      </button>
                    </div>
                    <div class="card-body">
                      <div id="experiences-container">
                        <div class="experience-item border rounded p-3 mb-3">
                          <div class="row g-3">
                            <div class="col-md-6">
                              <label class="form-label" for="company_name_0">{{ __('Company Name') }} <span class="text-danger">*</span></label>
                              <input type="text" id="company_name_0" name="experiences[0][company_name]" class="form-control" placeholder="{{ __('Company Name') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="experience_country_0">{{ __('Country') }} <span class="text-danger">*</span></label>
                              <select id="experience_country_0" name="experiences[0][country]" class="form-select">
                                <option value="">{{ __('Select Country') }}</option>
                                <option value="Saudi Arabia">{{ __('Saudi Arabia') }}</option>
                                <!-- Add more countries as needed -->
                              </select>
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="start_date_0">{{ __('Start Date') }} <span class="text-danger">*</span></label>
                              <input type="text" id="start_date_0" name="experiences[0][start_date]" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="end_date_0">{{ __('End Date') }} <span class="text-danger">*</span></label>
                              <input type="text" id="end_date_0" name="experiences[0][end_date]" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="job_title_0">{{ __('Job Title') }} <span class="text-danger">*</span></label>
                              <input type="text" id="job_title_0" name="experiences[0][job_title]" class="form-control" placeholder="{{ __('Job Title') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="reason_for_leaving_0">{{ __('Reason for Leaving') }}</label>
                              <input type="text" id="reason_for_leaving_0" name="experiences[0][reason_for_leaving]" class="form-control" placeholder="{{ __('Reason for Leaving') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="last_salary_0">{{ __('Last Salary') }}</label>
                              <input type="number" id="last_salary_0" name="experiences[0][last_salary]" class="form-control" placeholder="{{ __('Last Salary') }}" min="0" step="0.01" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="experience_notes_0">{{ __('Notes') }}</label>
                              <textarea id="experience_notes_0" name="experiences[0][notes]" class="form-control" placeholder="{{ __('Notes about this experience') }}" rows="2"></textarea>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Languages Tab -->
            <div id="languages" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('Languages') }}</h6>
                  <small>{{ __('Enter your language skills') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-languages-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="mb-0">{{ __('Language Skills') }}</h5>
                      <button type="button" class="btn btn-primary btn-sm" id="add-language">
                        <i class="ti ti-plus me-1"></i>{{ __('Add Language') }}
                      </button>
                    </div>
                    <div class="card-body">
                      <div id="languages-container">
                        <div class="language-item border rounded p-3 mb-3">
                          <div class="row g-3">
                            <div class="col-md-12">
                              <label class="form-label" for="language_name_0">{{ __('Language') }} <span class="text-danger">*</span></label>
                              <select id="language_name_0" name="languages[0][name]" class="form-select">
                                <option value="">{{ __('Select Language') }}</option>
                                <option value="Arabic">{{ __('Arabic') }}</option>
                                <option value="English">{{ __('English') }}</option>
                                <!-- Add more languages as needed -->
                              </select>
                            </div>
                            
                            <div class="col-md-4">
                              <label class="form-label" for="reading_level_0">{{ __('Reading Proficiency') }} <span class="text-danger">*</span></label>
                              <select id="reading_level_0" name="languages[0][reading]" class="form-select">
                                <option value="">{{ __('Select Level') }}</option>
                                <option value="Beginner">{{ __('Beginner') }}</option>
                                <option value="Intermediate">{{ __('Intermediate') }}</option>
                                <option value="Advanced">{{ __('Advanced') }}</option>
                                <option value="Native">{{ __('Native') }}</option>
                              </select>
                            </div>
                            
                            <div class="col-md-4">
                              <label class="form-label" for="writing_level_0">{{ __('Writing Proficiency') }} <span class="text-danger">*</span></label>
                              <select id="writing_level_0" name="languages[0][writing]" class="form-select">
                                <option value="">{{ __('Select Level') }}</option>
                                <option value="Beginner">{{ __('Beginner') }}</option>
                                <option value="Intermediate">{{ __('Intermediate') }}</option>
                                <option value="Advanced">{{ __('Advanced') }}</option>
                                <option value="Native">{{ __('Native') }}</option>
                              </select>
                            </div>
                            
                            <div class="col-md-4">
                              <label class="form-label" for="speaking_level_0">{{ __('Speaking Proficiency') }} <span class="text-danger">*</span></label>
                              <select id="speaking_level_0" name="languages[0][speaking]" class="form-select">
                                <option value="">{{ __('Select Level') }}</option>
                                <option value="Beginner">{{ __('Beginner') }}</option>
                                <option value="Intermediate">{{ __('Intermediate') }}</option>
                                <option value="Advanced">{{ __('Advanced') }}</option>
                                <option value="Native">{{ __('Native') }}</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- References Tab -->
            <div id="references" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('References') }}</h6>
                  <small>{{ __('Enter your professional references') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-references-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h5 class="mb-0">{{ __('Professional References') }}</h5>
                      <button type="button" class="btn btn-primary btn-sm" id="add-reference">
                        <i class="ti ti-plus me-1"></i>{{ __('Add Reference') }}
                      </button>
                    </div>
                    <div class="card-body">
                      <div id="references-container">
                        <div class="reference-item border rounded p-3 mb-3">
                          <div class="row g-3">
                            <div class="col-md-6">
                              <label class="form-label" for="reference_name_0">{{ __('Reference Name') }} <span class="text-danger">*</span></label>
                              <input type="text" id="reference_name_0" name="references[0][name]" class="form-control" placeholder="{{ __('Reference Name') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="reference_company_0">{{ __('Current Company') }} <span class="text-danger">*</span></label>
                              <input type="text" id="reference_company_0" name="references[0][company]" class="form-control" placeholder="{{ __('Current Company') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="reference_position_0">{{ __('Position') }} <span class="text-danger">*</span></label>
                              <input type="text" id="reference_position_0" name="references[0][position]" class="form-control" placeholder="{{ __('Position') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="reference_phone_0">{{ __('Phone Number') }} <span class="text-danger">*</span></label>
                              <input type="text" id="reference_phone_0" name="references[0][phone]" class="form-control" placeholder="{{ __('Phone Number') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="reference_email_0">{{ __('Email') }} <span class="text-danger">*</span></label>
                              <input type="email" id="reference_email_0" name="references[0][email]" class="form-control" placeholder="{{ __('Email') }}" required />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="reference_relationship_0">{{ __('Relationship') }} <span class="text-danger">*</span></label>
                              <input type="text" id="reference_relationship_0" name="references[0][relationship]" class="form-control" placeholder="{{ __('Relationship') }}" required />
                            </div>
                            
                            <div class="col-md-12">
                              <label class="form-label" for="reference_notes_0">{{ __('Notes') }}</label>
                              <textarea id="reference_notes_0" name="references[0][notes]" class="form-control" placeholder="{{ __('Notes about this reference') }}" rows="2"></textarea>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Other Information Tab -->
            <div id="other-info" class="content">
              <div class="content-header mb-3 d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">{{ __('Other Information') }}</h6>
                  <small>{{ __('Enter additional details') }}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-info" id="fill-other-info-sample">
                  <i class="ti ti-database me-1"></i>{{ __('Fill Sample Data') }}
                </button>
              </div>
              
              <div class="row g-3">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h5 class="mb-0">{{ __('Additional Information') }}</h5>
                    </div>
                    <div class="card-body">
                      <div class="row g-3">
                        <div class="col-md-6">
                          <label class="form-label" for="worked_before">{{ __('Have you worked for the company before?') }}</label>
                          <div class="form-check mt-2">
                            <input class="form-check-input" type="radio" name="worked_before" id="worked_before_yes" value="yes">
                            <label class="form-check-label" for="worked_before_yes">{{ __('Yes') }}</label>
                          </div>
                          <div class="form-check">
                            <input class="form-check-input" type="radio" name="worked_before" id="worked_before_no" value="no" checked>
                            <label class="form-check-label" for="worked_before_no">{{ __('No') }}</label>
                          </div>
                        </div>
                        
                        <div class="col-md-6 worked-before-details" style="display: none;">
                          <div class="row g-3">
                            <div class="col-md-6">
                              <label class="form-label" for="previous_start_date">{{ __('Previous Start Date') }}</label>
                              <input type="text" id="previous_start_date" name="previous_start_date" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="previous_end_date">{{ __('Previous End Date') }}</label>
                              <input type="text" id="previous_end_date" name="previous_end_date" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" />
                            </div>
                            
                            <div class="col-md-12">
                              <label class="form-label" for="previous_leaving_reason">{{ __('Reason for Leaving') }}</label>
                              <textarea id="previous_leaving_reason" name="previous_leaving_reason" class="form-control" placeholder="{{ __('Reason for leaving the company') }}" rows="2"></textarea>
                            </div>
                          </div>
                        </div>
                        
                        <div class="col-md-6">
                          <label class="form-label" for="current_employment_status">{{ __('Current Employment Status') }} <span class="text-danger">*</span></label>
                          <select id="current_employment_status" name="current_employment_status" class="form-select" required>
                            <option value="">{{ __('Select Status') }}</option>
                            <option value="employed">{{ __('Employed') }}</option>
                            <option value="unemployed">{{ __('Unemployed') }}</option>
                            <option value="self_employed">{{ __('Self-Employed') }}</option>
                            <option value="student">{{ __('Student') }}</option>
                          </select>
                        </div>
                        
                        <div class="col-md-6 current-employment-details" style="display: none;">
                          <div class="row g-3">
                            <div class="col-md-12">
                              <label class="form-label" for="current_company">{{ __('Current Company') }}</label>
                              <input type="text" id="current_company" name="current_company" class="form-control" placeholder="{{ __('Current Company') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="current_position">{{ __('Current Position') }}</label>
                              <input type="text" id="current_position" name="current_position" class="form-control" placeholder="{{ __('Current Position') }}" />
                            </div>
                            
                            <div class="col-md-6">
                              <label class="form-label" for="current_salary">{{ __('Current Salary') }}</label>
                              <input type="number" id="current_salary" name="current_salary" class="form-control" placeholder="{{ __('Current Salary') }}" min="0" step="0.01" />
                            </div>
                            
                            <div class="col-md-12">
                              <label class="form-label" for="contact_current_employer">{{ __('May we contact your current employer?') }}</label>
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="contact_current_employer" id="contact_current_employer_yes" value="yes">
                                <label class="form-check-label" for="contact_current_employer_yes">{{ __('Yes') }}</label>
                              </div>
                              <div class="form-check">
                                <input class="form-check-input" type="radio" name="contact_current_employer" id="contact_current_employer_no" value="no" checked>
                                <label class="form-check-label" for="contact_current_employer_no">{{ __('No') }}</label>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div class="col-md-6">
                          <label class="form-label" for="available_start_date">{{ __('Date Available to Start') }} <span class="text-danger">*</span></label>
                          <input type="text" id="available_start_date" name="available_start_date" class="form-control flatpickr-date" placeholder="YYYY-MM-DD" required />
                        </div>
                        
                        <div class="col-md-12">
                          <label class="form-label" for="additional_notes">{{ __('Additional Notes') }}</label>
                          <textarea id="additional_notes" name="additional_notes" class="form-control" placeholder="{{ __('Any additional information you would like to share') }}" rows="4"></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button type="button" class="btn btn-primary btn-next waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Next') }}</span>
                    <i class="ti ti-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <div id="review" class="content">
              <div class="content-header mb-3">
                <h6 class="mb-0">{{ __('Review and Submit') }}</h6>
                <small>{{ __('Review your information before submitting') }}</small>
              </div>
              
              <div class="row g-3">
                <div class="alert alert-primary" role="alert">
                  <div class="alert-body">
                    <p class="mb-0">{{ __('Please review all the information you previously provided and make sure it\'s correct. Click Submit to complete your application.') }}</p>
                  </div>
                </div>
                
                <div class="col-12 d-flex justify-content-between mt-4">
                  <button type="button" class="btn btn-label-secondary btn-prev waves-effect">
                    <i class="ti ti-arrow-left me-sm-1"></i>
                    <span class="align-middle d-sm-inline-block d-none">{{ __('Previous') }}</span>
                  </button>
                  <button class="btn btn-success btn-submit waves-effect waves-light">
                    <span class="align-middle d-sm-inline-block d-none me-sm-1">{{ __('Submit') }}</span>
                    <i class="ti ti-check"></i>
                  </button>
                </div>
              </div>
            </div>
            
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection 