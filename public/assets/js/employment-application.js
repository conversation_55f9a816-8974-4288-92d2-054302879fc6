/**
 * Employment Application Form
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
  // Stepper element
  const employmentApplicationWizard = document.querySelector('.bs-stepper.vertical');
  
  // Initialize Stepper
  let stepper;
  if (employmentApplicationWizard) {
    stepper = new Stepper(employmentApplicationWizard, {
      linear: false // <-- Keep as false to allow clicking directly on steps
    });

    // Get Next/Previous buttons
    const btnNext = [].slice.call(employmentApplicationWizard.querySelectorAll('.btn-next'));
    const btnPrev = [].slice.call(employmentApplicationWizard.querySelectorAll('.btn-prev'));
    const btnSubmit = document.querySelector('.btn-submit');
    const form = document.getElementById('employment-application-form');

    // Next button click handler
    if (btnNext) {
      btnNext.forEach(button => {
        button.addEventListener('click', event => {
          // Allow navigation without strict validation - users can come back to fill fields
          stepper.next();
          // Scroll to top of the content section after step change
          const bsStepperContent = document.querySelector('.bs-stepper-content');
          if (bsStepperContent) {
            bsStepperContent.scrollTop = 0;
          }
        });
      });
    }

    // Previous button click handler
    if (btnPrev) {
      btnPrev.forEach(button => {
        button.addEventListener('click', event => {
          stepper.previous();
          // Scroll to top of the content section after step change
          const bsStepperContent = document.querySelector('.bs-stepper-content');
          if (bsStepperContent) {
            bsStepperContent.scrollTop = 0;
          }
        });
      });
    }

    // Submit button click handler
    if (btnSubmit) {
      btnSubmit.addEventListener('click', event => {
        event.preventDefault();

        // Validate required fields (basic information and employment details)
        if (validateBasicInformation()) {
          // Submit the form to the database
          form.submit();
        } else {
          // Show user-friendly validation message
          alert('Please fill in all required fields (Basic Information and Employment Details) before submitting.');
          // Navigate to Basic Information step
          stepper.to(1);
        }
      });
    }



    // Enable clicking on step headers to navigate
    const stepHeaders = employmentApplicationWizard.querySelectorAll('.step-trigger');
    if (stepHeaders) {
      stepHeaders.forEach(header => {
        header.style.cursor = 'pointer';
      });
    }

    // Flatpickr initialization
    const flatpickrDate = document.querySelectorAll('.flatpickr-date');
    if (flatpickrDate) {
      flatpickrDate.forEach(date => {
        date.flatpickr({
          allowInput: true,
          dateFormat: 'Y-m-d'
        });
      });
    }

    // Show/hide children count based on marital status
    const maritalStatusSelect = document.getElementById('marital_status');
    const childrenDiv = document.getElementById('children_div');
    
    if (maritalStatusSelect && childrenDiv) {
      maritalStatusSelect.addEventListener('change', function() {
        if (this.value === 'married' || this.value === 'divorced' || this.value === 'widowed') {
          childrenDiv.style.display = 'block';
        } else {
          childrenDiv.style.display = 'none';
        }
      });
    }

    // Show/hide previous employment details
    const workedBeforeRadios = document.querySelectorAll('input[name="worked_before"]');
    const workedBeforeDetails = document.querySelector('.worked-before-details');
    
    if (workedBeforeRadios.length && workedBeforeDetails) {
      workedBeforeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
          workedBeforeDetails.style.display = this.value === 'yes' ? 'block' : 'none';
        });
      });
    }
    
    // Show/hide current employment details
    const employmentStatusSelect = document.getElementById('current_employment_status');
    const currentEmploymentDetails = document.querySelector('.current-employment-details');
    
    if (employmentStatusSelect && currentEmploymentDetails) {
      employmentStatusSelect.addEventListener('change', function() {
        currentEmploymentDetails.style.display = this.value === 'employed' ? 'block' : 'none';
      });
    }

    // Add certificate button
    const addCertificateBtn = document.getElementById('add-certificate');
    const certificatesContainer = document.getElementById('certificates-container');
    
    if (addCertificateBtn && certificatesContainer) {
      let certificateCount = 1;
      
      addCertificateBtn.addEventListener('click', function() {
        // Create the elements programmatically instead of using template literals with Blade syntax
        const newCertificateItem = document.createElement('div');
        newCertificateItem.className = 'certificate-item border rounded p-3 mb-3';
        
        const rowDiv = document.createElement('div');
        rowDiv.className = 'row g-3';
        
        // Remove button
        const removeButtonCol = document.createElement('div');
        removeButtonCol.className = 'col-12 d-flex justify-content-end';
        
        const removeButton = document.createElement('button');
        removeButton.type = 'button';
        removeButton.className = 'btn btn-sm btn-danger remove-certificate';
        removeButton.innerHTML = '<i class="ti ti-x me-1"></i>Remove';
        removeButton.addEventListener('click', function() {
          this.closest('.certificate-item').remove();
        });
        
        removeButtonCol.appendChild(removeButton);
        rowDiv.appendChild(removeButtonCol);
        
        // Certificate Name
        const certNameCol = document.createElement('div');
        certNameCol.className = 'col-md-6';
        
        const certNameLabel = document.createElement('label');
        certNameLabel.className = 'form-label';
        certNameLabel.setAttribute('for', `certificate_name_${certificateCount}`);
        certNameLabel.innerHTML = 'Certificate Name <span class="text-danger">*</span>';
        
        const certNameInput = document.createElement('input');
        certNameInput.type = 'text';
        certNameInput.id = `certificate_name_${certificateCount}`;
        certNameInput.name = `certificates[${certificateCount}][name]`;
        certNameInput.className = 'form-control';
        certNameInput.placeholder = 'Certificate Name';
        certNameInput.required = true;
        
        certNameCol.appendChild(certNameLabel);
        certNameCol.appendChild(certNameInput);
        rowDiv.appendChild(certNameCol);
        
        // Specialization
        const specCol = document.createElement('div');
        specCol.className = 'col-md-6';
        
        const specLabel = document.createElement('label');
        specLabel.className = 'form-label';
        specLabel.setAttribute('for', `certificate_specialization_${certificateCount}`);
        specLabel.innerHTML = 'Specialization <span class="text-danger">*</span>';
        
        const specInput = document.createElement('input');
        specInput.type = 'text';
        specInput.id = `certificate_specialization_${certificateCount}`;
        specInput.name = `certificates[${certificateCount}][specialization]`;
        specInput.className = 'form-control';
        specInput.placeholder = 'Specialization';
        specInput.required = true;
        
        specCol.appendChild(specLabel);
        specCol.appendChild(specInput);
        rowDiv.appendChild(specCol);
        
        // Institution Name
        const instCol = document.createElement('div');
        instCol.className = 'col-md-6';
        
        const instLabel = document.createElement('label');
        instLabel.className = 'form-label';
        instLabel.setAttribute('for', `certificate_institution_${certificateCount}`);
        instLabel.innerHTML = 'Institution Name <span class="text-danger">*</span>';
        
        const instInput = document.createElement('input');
        instInput.type = 'text';
        instInput.id = `certificate_institution_${certificateCount}`;
        instInput.name = `certificates[${certificateCount}][institution]`;
        instInput.className = 'form-control';
        instInput.placeholder = 'Institution Name';
        instInput.required = true;
        
        instCol.appendChild(instLabel);
        instCol.appendChild(instInput);
        rowDiv.appendChild(instCol);
        
        // Country
        const countryCol = document.createElement('div');
        countryCol.className = 'col-md-6';
        
        const countryLabel = document.createElement('label');
        countryLabel.className = 'form-label';
        countryLabel.setAttribute('for', `certificate_country_${certificateCount}`);
        countryLabel.innerHTML = 'Country <span class="text-danger">*</span>';
        
        const countrySelect = document.createElement('select');
        countrySelect.id = `certificate_country_${certificateCount}`;
        countrySelect.name = `certificates[${certificateCount}][country]`;
        countrySelect.className = 'form-select';
        countrySelect.required = true;
        
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select Country';
        
        const saudiOption = document.createElement('option');
        saudiOption.value = 'Saudi Arabia';
        saudiOption.textContent = 'Saudi Arabia';
        
        countrySelect.appendChild(defaultOption);
        countrySelect.appendChild(saudiOption);
        
        countryCol.appendChild(countryLabel);
        countryCol.appendChild(countrySelect);
        rowDiv.appendChild(countryCol);
        
        // Graduation Year
        const yearCol = document.createElement('div');
        yearCol.className = 'col-md-6';
        
        const yearLabel = document.createElement('label');
        yearLabel.className = 'form-label';
        yearLabel.setAttribute('for', `certificate_year_${certificateCount}`);
        yearLabel.innerHTML = 'Graduation Year <span class="text-danger">*</span>';
        
        const yearInput = document.createElement('input');
        yearInput.type = 'number';
        yearInput.id = `certificate_year_${certificateCount}`;
        yearInput.name = `certificates[${certificateCount}][year]`;
        yearInput.className = 'form-control';
        yearInput.placeholder = 'Graduation Year';
        yearInput.min = '1950';
        yearInput.max = new Date().getFullYear().toString();
        yearInput.required = true;
        
        yearCol.appendChild(yearLabel);
        yearCol.appendChild(yearInput);
        rowDiv.appendChild(yearCol);
        
        // Grade/GPA
        const gradeCol = document.createElement('div');
        gradeCol.className = 'col-md-6';
        
        const gradeLabel = document.createElement('label');
        gradeLabel.className = 'form-label';
        gradeLabel.setAttribute('for', `certificate_grade_${certificateCount}`);
        gradeLabel.innerHTML = 'Grade/GPA <span class="text-danger">*</span>';
        
        const gradeInput = document.createElement('input');
        gradeInput.type = 'text';
        gradeInput.id = `certificate_grade_${certificateCount}`;
        gradeInput.name = `certificates[${certificateCount}][grade]`;
        gradeInput.className = 'form-control';
        gradeInput.placeholder = 'Grade/GPA';
        gradeInput.required = true;
        
        gradeCol.appendChild(gradeLabel);
        gradeCol.appendChild(gradeInput);
        rowDiv.appendChild(gradeCol);
        
        // Append all to the container
        newCertificateItem.appendChild(rowDiv);
        certificatesContainer.appendChild(newCertificateItem);
        
        certificateCount++;
      });
    }

    // Add course button
    const addCourseBtn = document.getElementById('add-course');
    const coursesContainer = document.getElementById('courses-container');
    
    if (addCourseBtn && coursesContainer) {
      let courseCount = 1;
      
      addCourseBtn.addEventListener('click', function() {
        // Create a new course item similar to the certificate item above
        const newCourseItem = document.createElement('div');
        newCourseItem.className = 'course-item border rounded p-3 mb-3';
        
        const rowDiv = document.createElement('div');
        rowDiv.className = 'row g-3';
        
        // Remove button
        const removeButtonCol = document.createElement('div');
        removeButtonCol.className = 'col-12 d-flex justify-content-end';
        
        const removeButton = document.createElement('button');
        removeButton.type = 'button';
        removeButton.className = 'btn btn-sm btn-danger remove-course';
        removeButton.innerHTML = '<i class="ti ti-x me-1"></i>Remove';
        removeButton.addEventListener('click', function() {
          this.closest('.course-item').remove();
        });
        
        removeButtonCol.appendChild(removeButton);
        rowDiv.appendChild(removeButtonCol);
        
        // Create and append other course fields similar to the certificate pattern
        // (For brevity, I'm not including all fields, but they would follow the same pattern)
        
        // Course Name
        const courseNameCol = document.createElement('div');
        courseNameCol.className = 'col-md-6';
        
        const courseNameLabel = document.createElement('label');
        courseNameLabel.className = 'form-label';
        courseNameLabel.setAttribute('for', `course_name_${courseCount}`);
        courseNameLabel.innerHTML = 'Course Name <span class="text-danger">*</span>';
        
        const courseNameInput = document.createElement('input');
        courseNameInput.type = 'text';
        courseNameInput.id = `course_name_${courseCount}`;
        courseNameInput.name = `courses[${courseCount}][name]`;
        courseNameInput.className = 'form-control';
        courseNameInput.placeholder = 'Course Name';
        courseNameInput.required = true;
        
        courseNameCol.appendChild(courseNameLabel);
        courseNameCol.appendChild(courseNameInput);
        rowDiv.appendChild(courseNameCol);
        
        // Provider
        const providerCol = document.createElement('div');
        providerCol.className = 'col-md-6';
        
        const providerLabel = document.createElement('label');
        providerLabel.className = 'form-label';
        providerLabel.setAttribute('for', `course_provider_${courseCount}`);
        providerLabel.innerHTML = 'Training Provider <span class="text-danger">*</span>';
        
        const providerInput = document.createElement('input');
        providerInput.type = 'text';
        providerInput.id = `course_provider_${courseCount}`;
        providerInput.name = `courses[${courseCount}][provider]`;
        providerInput.className = 'form-control';
        providerInput.placeholder = 'Training Provider';
        providerInput.required = true;
        
        providerCol.appendChild(providerLabel);
        providerCol.appendChild(providerInput);
        rowDiv.appendChild(providerCol);
        
        // Append the new course item to the container
        newCourseItem.appendChild(rowDiv);
        coursesContainer.appendChild(newCourseItem);
        
        courseCount++;
      });
    }
    
    // Add experience button
    const addExperienceBtn = document.getElementById('add-experience');
    if (addExperienceBtn) {
      let experienceCount = 1;
      addExperienceBtn.addEventListener('click', function() {
        // Implement similar to certificate and course items
        console.log('Add experience clicked');
      });
    }
    
    // Add language button
    const addLanguageBtn = document.getElementById('add-language');
    if (addLanguageBtn) {
      let languageCount = 1;
      addLanguageBtn.addEventListener('click', function() {
        // Implement similar to certificate and course items
        console.log('Add language clicked');
      });
    }
    
    // Add reference button
    const addReferenceBtn = document.getElementById('add-reference');
    if (addReferenceBtn) {
      let referenceCount = 1;
      addReferenceBtn.addEventListener('click', function() {
        // Implement similar to certificate and course items
        console.log('Add reference clicked');
      });
    }

    // Sample Data Button Event Listeners
    const fillBasicInfoBtn = document.getElementById('fill-basic-info-sample');
    const fillCertificatesBtn = document.getElementById('fill-certificates-sample');
    const fillCoursesBtn = document.getElementById('fill-courses-sample');
    const fillExperienceBtn = document.getElementById('fill-experience-sample');
    const fillLanguagesBtn = document.getElementById('fill-languages-sample');
    const fillReferencesBtn = document.getElementById('fill-references-sample');
    const fillOtherInfoBtn = document.getElementById('fill-other-info-sample');

    if (fillBasicInfoBtn) {
      fillBasicInfoBtn.addEventListener('click', fillBasicInfoSample);
    }

    if (fillCertificatesBtn) {
      fillCertificatesBtn.addEventListener('click', fillCertificatesSample);
    }

    if (fillCoursesBtn) {
      fillCoursesBtn.addEventListener('click', fillCoursesSample);
    }

    if (fillExperienceBtn) {
      fillExperienceBtn.addEventListener('click', fillExperienceSample);
    }

    if (fillLanguagesBtn) {
      fillLanguagesBtn.addEventListener('click', fillLanguagesSample);
    }

    if (fillReferencesBtn) {
      fillReferencesBtn.addEventListener('click', fillReferencesSample);
    }

    if (fillOtherInfoBtn) {
      fillOtherInfoBtn.addEventListener('click', fillOtherInfoSample);
    }
  }

  // Step validation function
  function validateStep(stepIndex) {
    // Implement step-specific validation logic
    let isValid = true;
    
    // Get all required fields in current step
    const currentStep = document.querySelector('.bs-stepper-content .content:nth-child(' + (stepIndex + 1) + ')');
    if (currentStep) {
      const requiredFields = currentStep.querySelectorAll('[required]');
      
      // Check if all required fields are filled
      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          isValid = false;
          field.classList.add('is-invalid');
          
          // Add error message if not exists
          if (!field.nextElementSibling || !field.nextElementSibling.classList.contains('invalid-feedback')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = 'This field is required.';
            field.parentNode.insertBefore(errorDiv, field.nextSibling);
          }
        } else {
          field.classList.remove('is-invalid');
          // Remove error message if exists
          if (field.nextElementSibling && field.nextElementSibling.classList.contains('invalid-feedback')) {
            field.nextElementSibling.remove();
          }
        }
      });
    }
    
    return isValid;
  }

  // Form validation function
  function validateForm() {
    let isValid = true;
    
    // Get all required fields in the form
    const requiredFields = document.querySelectorAll('[required]');
    
    // Check if all required fields are filled
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        isValid = false;
        field.classList.add('is-invalid');
        
        // Add error message if not exists
        if (!field.nextElementSibling || !field.nextElementSibling.classList.contains('invalid-feedback')) {
          const errorDiv = document.createElement('div');
          errorDiv.className = 'invalid-feedback';
          errorDiv.textContent = 'This field is required.';
          field.parentNode.insertBefore(errorDiv, field.nextSibling);
        }
      } else {
        field.classList.remove('is-invalid');
        // Remove error message if exists
        if (field.nextElementSibling && field.nextElementSibling.classList.contains('invalid-feedback')) {
          field.nextElementSibling.remove();
        }
      }
    });
    
    return isValid;
  }

  // Validate required fields across all sections
  function validateBasicInformation() {
    let isValid = true;

    // Clear any existing validation states
    document.querySelectorAll('.is-invalid').forEach(field => {
      field.classList.remove('is-invalid');
    });
    document.querySelectorAll('.invalid-feedback').forEach(msg => {
      msg.remove();
    });

    // Get required fields in basic information section (step 1)
    const basicInfoSection = document.querySelector('#basic-info');
    if (basicInfoSection) {
      const basicRequiredFields = basicInfoSection.querySelectorAll('[required]');

      basicRequiredFields.forEach(field => {
        if (!field.value.trim()) {
          isValid = false;
          field.classList.add('is-invalid');

          // Add error message if not exists
          if (!field.nextElementSibling || !field.nextElementSibling.classList.contains('invalid-feedback')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = 'This field is required.';
            field.parentNode.insertBefore(errorDiv, field.nextSibling);
          }
        }
      });
    }

    // Also check required fields in other information section (step 7) - employment status and start date
    const otherInfoSection = document.querySelector('#other-info');
    if (otherInfoSection) {
      const employmentStatusField = otherInfoSection.querySelector('#current_employment_status');
      const startDateField = otherInfoSection.querySelector('#available_start_date');

      [employmentStatusField, startDateField].forEach(field => {
        if (field && !field.value.trim()) {
          isValid = false;
          field.classList.add('is-invalid');

          // Add error message if not exists
          if (!field.nextElementSibling || !field.nextElementSibling.classList.contains('invalid-feedback')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = 'This field is required.';
            field.parentNode.insertBefore(errorDiv, field.nextSibling);
          }
        }
      });
    }

    return isValid;
  }

  // Sample Data Functions
  function fillBasicInfoSample() {
    // Fill personal information
    document.getElementById('first_name').value = 'Ahmed';
    document.getElementById('father_name').value = 'Mohammed';
    document.getElementById('grandfather_name').value = 'Abdullah';
    document.getElementById('family_name').value = 'Al-Rashid';
    document.getElementById('mother_name').value = 'Fatima Al-Zahra';
    document.getElementById('nationality').value = 'Saudi';
    document.getElementById('birth_date').value = '1990-05-15';
    document.getElementById('birth_place').value = 'Riyadh';
    document.getElementById('religion').value = 'Islam';
    document.getElementById('gender').value = 'male';
    document.getElementById('marital_status').value = 'single';
    
    // Fill ID information
    document.getElementById('id_type').value = 'national_id';
    document.getElementById('id_number').value = '1234567890';
    document.getElementById('id_issue_date').value = '2020-01-01';
    document.getElementById('id_issue_place').value = 'Riyadh';
    document.getElementById('id_expiry_date').value = '2030-01-01';
    
    // Fill address information
    document.getElementById('country').value = 'Saudi Arabia';
    document.getElementById('city').value = 'Riyadh';
    document.getElementById('district').value = 'Al Malaz';
    document.getElementById('street').value = 'King Fahd Road';
    document.getElementById('po_box').value = '12345';
    document.getElementById('postal_code').value = '11564';
    
    // Fill contact information
    document.getElementById('home_phone').value = '+966114567890';
    document.getElementById('mobile_phone').value = '+966501234567';
    document.getElementById('email').value = '<EMAIL>';
  }

  function fillCertificatesSample() {
    // Fill first certificate
    document.getElementById('certificate_name_0').value = 'Bachelor of Computer Science';
    document.getElementById('certificate_specialization_0').value = 'Software Engineering';
    document.getElementById('certificate_institution_0').value = 'King Saud University';
    document.getElementById('certificate_country_0').value = 'Saudi Arabia';
    document.getElementById('certificate_year_0').value = '2015';
    document.getElementById('certificate_grade_0').value = 'Excellent';
  }

  function fillCoursesSample() {
    // Fill first course
    document.getElementById('course_name_0').value = 'Laravel Development';
    document.getElementById('course_provider_0').value = 'Tech Academy';
    document.getElementById('course_start_date_0').value = '2020-01-01';
    document.getElementById('course_end_date_0').value = '2020-03-01';
    document.getElementById('course_hours_0').value = '120';
    document.getElementById('course_type_0').value = 'technical';
    document.getElementById('course_cost_0').value = '2500';
    document.getElementById('course_notes_0').value = 'Completed with certification';
    
    // Trigger change events to ensure form validation updates
    document.getElementById('course_name_0').dispatchEvent(new Event('change'));
    document.getElementById('course_provider_0').dispatchEvent(new Event('change'));
    document.getElementById('course_end_date_0').dispatchEvent(new Event('change'));
  }

  function fillExperienceSample() {
    // Fill first experience
    document.getElementById('company_name_0').value = 'Tech Solutions Inc.';
    document.getElementById('experience_country_0').value = 'Saudi Arabia';
    document.getElementById('start_date_0').value = '2018-06-01';
    document.getElementById('end_date_0').value = '2023-05-31';
    document.getElementById('job_title_0').value = 'Software Developer';
    document.getElementById('last_salary_0').value = '8000';
    document.getElementById('reason_for_leaving_0').value = 'Career advancement';
  }

  function fillLanguagesSample() {
    // Fill first language
    document.getElementById('language_name_0').value = 'Arabic';
    document.getElementById('reading_level_0').value = 'Native';
    document.getElementById('writing_level_0').value = 'Native';
    document.getElementById('speaking_level_0').value = 'Native';
  }

  function fillReferencesSample() {
    // Fill first reference
    document.getElementById('reference_name_0').value = 'Dr. Sarah Al-Mahmoud';
    document.getElementById('reference_company_0').value = 'Tech Solutions Inc.';
    document.getElementById('reference_position_0').value = 'Senior Manager';
    document.getElementById('reference_phone_0').value = '+966501987654';
    document.getElementById('reference_email_0').value = '<EMAIL>';
    document.getElementById('reference_relationship_0').value = 'Former Supervisor';
    document.getElementById('reference_notes_0').value = 'Former direct supervisor';
  }

  function fillOtherInfoSample() {
    // Fill employment status
    document.getElementById('current_employment_status').value = 'employed';
    document.getElementById('available_start_date').value = '2025-08-01';
    document.getElementById('additional_notes').value = 'Looking for new challenges and growth opportunities in a dynamic environment.';
    
    // Trigger the employment status change to show additional fields
    const event = new Event('change');
    document.getElementById('current_employment_status').dispatchEvent(event);
    
    // Fill current employment details after a short delay
    setTimeout(() => {
      const currentCompany = document.getElementById('current_company');
      const currentPosition = document.getElementById('current_position');
      const currentSalary = document.getElementById('current_salary');
      
      if (currentCompany) currentCompany.value = 'Current Tech Company';
      if (currentPosition) currentPosition.value = 'Senior Developer';
      if (currentSalary) currentSalary.value = '10000';
    }, 100);
  }

}); 