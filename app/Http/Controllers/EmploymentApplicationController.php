<?php

namespace App\Http\Controllers;

use App\Models\EmploymentApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class EmploymentApplicationController extends Controller
{
    /**
     * Display the employment application form.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $breadcrumbs = [
            ['link' => route('dashboard'), 'name' => __('Dashboard')],
            ['name' => __('Employment Application')]
        ];

        return view('employment-application.index', compact('breadcrumbs'));
    }

    /**
     * Store the employment application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Debug: Log the incoming request data
        \Log::info('Employment Application Submission Data:', $request->all());
        
        // Validate the main application data - Only Basic Information is required
        $validator = Validator::make($request->all(), [
            // Personal Information (REQUIRED - Basic Info)
            'first_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'grandfather_name' => 'required|string|max:255',
            'family_name' => 'required|string|max:255',
            'mother_name' => 'required|string|max:255',
            'nationality' => 'required|string|max:255',
            'birth_date' => 'required|date',
            'birth_place' => 'required|string|max:255',
            'religion' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'marital_status' => 'required|in:single,married,divorced,widowed',
            'children_count' => 'nullable|integer|min:0',
            
            // ID Information (REQUIRED - Basic Info)
            'id_type' => 'required|in:national_id,iqama,passport',
            'id_number' => 'required|string|max:255',
            'id_issue_date' => 'required|date',
            'id_issue_place' => 'required|string|max:255',
            'id_expiry_date' => 'required|date|after:today',
            
            // Address Information (REQUIRED - Basic Info)
            'country' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'district' => 'required|string|max:255',
            'street' => 'required|string|max:255',
            'po_box' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            
            // Contact Information (REQUIRED - Basic Info)
            'home_phone' => 'nullable|string|max:255',
            'mobile_phone' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            
            // Employment History (OPTIONAL)
            'worked_before' => 'nullable|boolean',
            'previous_start_date' => 'nullable|date',
            'previous_end_date' => 'nullable|date|after_or_equal:previous_start_date',
            'previous_leaving_reason' => 'nullable|string',
            
            // Current Employment Status (REQUIRED)
            'current_employment_status' => 'required|in:employed,unemployed,self_employed,student',
            'current_company' => 'nullable|string|max:255',
            'current_position' => 'nullable|string|max:255',
            'current_salary' => 'nullable|numeric|min:0',
            'contact_current_employer' => 'nullable|boolean',

            // Application Details (REQUIRED)
            'available_start_date' => 'required|date|after_or_equal:today',
            'additional_notes' => 'nullable|string',
            
            // Associated data validation (ALL OPTIONAL)
            'certificates' => 'nullable|array',
            'certificates.*.name' => 'nullable|string|max:255',
            'certificates.*.institution' => 'nullable|string|max:255',
            'certificates.*.graduation_date' => 'nullable|date',
            'certificates.*.grade' => 'nullable|string|max:255',
            
            'courses' => 'nullable|array',
            'courses.*.name' => 'nullable|string|max:255',
            'courses.*.institution' => 'nullable|string|max:255',
            'courses.*.completion_date' => 'nullable|date',
            'courses.*.duration' => 'nullable|string|max:255',
            
            'experiences' => 'nullable|array',
            'experiences.*.company_name' => 'nullable|string|max:255',
            'experiences.*.start_date' => 'nullable|date',
            'experiences.*.end_date' => 'nullable|date|after_or_equal:experiences.*.start_date',
            'experiences.*.job_title' => 'nullable|string|max:255',
            'experiences.*.reason_for_leaving' => 'nullable|string',
            'experiences.*.last_salary' => 'nullable|numeric|min:0',
            
            'languages' => 'nullable|array',
            'languages.*.name' => 'nullable|string|max:255',
            'languages.*.reading' => 'nullable|in:Beginner,Intermediate,Advanced,Native',
            'languages.*.writing' => 'nullable|in:Beginner,Intermediate,Advanced,Native',
            'languages.*.speaking' => 'nullable|in:Beginner,Intermediate,Advanced,Native',
            
            'references' => 'nullable|array',
            'references.*.name' => 'nullable|string|max:255',
            'references.*.company' => 'nullable|string|max:255',
            'references.*.position' => 'nullable|string|max:255',
            'references.*.phone' => 'nullable|string|max:255',
            'references.*.email' => 'nullable|email|max:255',
            'references.*.relationship' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            \Log::error('Employment Application Validation Failed:', $validator->errors()->toArray());
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Create the main employment application
            $applicationData = $request->only([
                'first_name', 'father_name', 'grandfather_name', 'family_name', 'mother_name',
                'nationality', 'birth_date', 'birth_place', 'religion', 'gender',
                'marital_status', 'children_count', 'id_type', 'id_number',
                'id_issue_date', 'id_issue_place', 'id_expiry_date',
                'country', 'city', 'district', 'street', 'po_box', 'postal_code',
                'home_phone', 'mobile_phone', 'email', 'worked_before',
                'previous_start_date', 'previous_end_date', 'previous_leaving_reason',
                'current_employment_status', 'current_company', 'current_position',
                'current_salary', 'contact_current_employer', 'available_start_date',
                'additional_notes'
            ]);

            // Set default values for boolean fields
            $applicationData['worked_before'] = $request->boolean('worked_before');
            $applicationData['contact_current_employer'] = $request->boolean('contact_current_employer');

            $application = EmploymentApplication::create($applicationData);

            // Store certificates
            if ($request->has('certificates')) {
                foreach ($request->certificates as $certificate) {
                    if (!empty($certificate['name'])) {
                        $application->certificates()->create($certificate);
                    }
                }
            }

            // Store courses
            if ($request->has('courses')) {
                foreach ($request->courses as $course) {
                    if (!empty($course['name'])) {
                        $application->courses()->create($course);
                    }
                }
            }

            // Store experiences
            if ($request->has('experiences')) {
                foreach ($request->experiences as $experience) {
                    if (!empty($experience['company_name'])) {
                        $application->experiences()->create($experience);
                    }
                }
            }

            // Store languages
            if ($request->has('languages')) {
                foreach ($request->languages as $language) {
                    if (!empty($language['name'])) {
                        $application->languages()->create($language);
                    }
                }
            }

            // Store references
            if ($request->has('references')) {
                foreach ($request->references as $reference) {
                    if (!empty($reference['name'])) {
                        $application->references()->create($reference);
                    }
                }
            }

            DB::commit();

            // Redirect with success message
            return redirect()->route('employment-application.success')
                ->with('success', __('Your application has been submitted successfully!'))
                ->with('application_id', $application->application_reference);

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Log the actual exception details
            \Log::error('Employment Application Submission Failed:', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->back()
                ->with('error', __('An error occurred while submitting your application. Please try again.'))
                ->withInput();
        }
    }

    /**
     * Display the success page.
     *
     * @return \Illuminate\View\View
     */
    public function success()
    {
        $breadcrumbs = [
            ['link' => route('dashboard'), 'name' => __('Dashboard')],
            ['link' => route('employment-application.index'), 'name' => __('Employment Application')],
            ['name' => __('Application Submitted')]
        ];

        return view('employment-application.success', compact('breadcrumbs'));
    }
} 