<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;

class EmploymentApplicationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that the employment application page can be accessed.
     *
     * @return void
     */
    public function test_employment_application_page_can_be_accessed()
    {
        // Create Admin role if it doesn't exist
        $adminRole = Role::firstOrCreate(['name' => 'Admin']);

        $user = User::factory()->create();
        $user->assignRole($adminRole);

        $response = $this->actingAs($user)
                         ->get(route('employment-application.index'));

        $response->assertStatus(200);
        $response->assertViewIs('employment-application.index');
    }

    /**
     * Test that the employment application can be submitted.
     *
     * @return void
     */
    public function test_employment_application_can_be_submitted()
    {
        // Create Admin role if it doesn't exist
        $adminRole = Role::firstOrCreate(['name' => 'Admin']);

        $user = User::factory()->create();
        $user->assignRole($adminRole);
        
        $response = $this->actingAs($user)
                         ->post(route('employment-application.store'), [
                             'first_name' => $this->faker->firstName,
                             'father_name' => $this->faker->firstName,
                             'grandfather_name' => $this->faker->firstName,
                             'family_name' => $this->faker->lastName,
                             'mother_name' => $this->faker->firstName,
                             'nationality' => 'Saudi',
                             'birth_date' => '1990-01-01',
                             'birth_place' => $this->faker->city,
                             'religion' => 'Islam',
                             'gender' => 'male',
                             'marital_status' => 'single',
                             'id_type' => 'national_id',
                             'id_number' => $this->faker->numerify('##########'),
                             'id_issue_date' => '2015-01-01',
                             'id_issue_place' => $this->faker->city,
                             'id_expiry_date' => '2030-01-01',
                             'country' => 'Saudi Arabia',
                             'city' => $this->faker->city,
                             'district' => $this->faker->word,
                             'street' => $this->faker->streetName,
                             'mobile_phone' => $this->faker->phoneNumber,
                             'email' => $this->faker->email,
                             'certificates' => [
                                 [
                                     'name' => 'Bachelor Degree',
                                     'specialization' => 'Computer Science',
                                     'institution' => 'University of ' . $this->faker->city,
                                     'country' => 'Saudi Arabia',
                                     'year' => '2010',
                                     'grade' => 'Excellent'
                                 ]
                             ],
                             'courses' => [
                                 [
                                     'name' => 'Web Development',
                                     'provider' => 'Training Center',
                                     'start_date' => '2018-01-01',
                                     'end_date' => '2018-02-01',
                                     'hours' => '40',
                                     'type' => 'technical',
                                     'cost' => '1000',
                                     'notes' => 'Completed successfully'
                                 ]
                             ]
                         ]);

        $response->assertRedirect(route('employment-application.success'));
        $response->assertSessionHas('success');
    }

    /**
     * Test that the success page can be accessed.
     *
     * @return void
     */
    public function test_success_page_can_be_accessed()
    {
        // Create Admin role if it doesn't exist
        $adminRole = Role::firstOrCreate(['name' => 'Admin']);

        $user = User::factory()->create();
        $user->assignRole($adminRole);
        
        $response = $this->actingAs($user)
                         ->get(route('employment-application.success'));

        $response->assertStatus(200);
        $response->assertViewIs('employment-application.success');
    }
} 